<template>
  <div
    v-if="totalCount"
    class="fixed right-5 bottom-5 z-999 w-[460px] h-[100px] flex bg-white items-center justify-between border-[4px] border-[#6d3202] rounded-full"
  >
    <div class="flex items-center relative px-4 py-2">
      <div class="relative">
        <img :src="IconCart" class="w-[50px] h-[50px]" />
        <div
          v-if="totalCount"
          class="absolute -top-3 -right-3 min-w-[28px] h-[28px] bg-[#6d3202] rounded-full flex items-center justify-center text-white text-[18px] px-1"
        >
          {{ totalCount > 99 ? '99+' : totalCount }}
        </div>
      </div>

      <!-- 总价显示 -->
      <div v-if="total" class="ml-5 text-[#6d3202] font-bold flex flex-col gap-1">
        <div class="leading-none">
          <text class="text-[24px]">¥</text>
          <text class="text-[36px] ml-1">{{ computedPrice?.totalPrice }}</text>
        </div>

        <div v-if="computedPrice?.totalPrice !== total" class="text-sm text-[#1a1a1a] ml-1">
          <text v-if="computedPrice?.couponId">券后</text>
          <text>已优惠¥{{ computedPrice?.totalDiscountAmount }}</text>
        </div>
      </div>
    </div>

    <!-- 结算按钮 -->
    <div class="px-4 py-2">
      <button
        class="bg-[#6d3202] text-white px-6 py-2 rounded-full text-sm w-[180px] h-[60px] text-[20px] transition-all duration-200 active:scale-[0.95]"
        :class="{ 'opacity-50': !items.length }"
        :disabled="!items.length"
        @click="handlePay"
      >
        去结算
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCart } from '../../useCart'
import { useRouter } from 'vue-router'
import IconCart from './imgs/cart.png'

const { items, total, totalCount, computedPrice } = useCart()
const router = useRouter()

const handlePay = () => {
  router.push('/consumer/payments')
}
</script>

<style scoped>
.bg-primary {
  background-color: #85310f;
}
</style>
