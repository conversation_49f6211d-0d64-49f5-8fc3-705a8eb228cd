#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Paths
const VERSION_FILE = path.join(__dirname, '..', 'build-version.json')
const ASSETS_DIR = path.join(__dirname, '..', 'src', 'renderer', 'src', 'assets')
const OUT_DIR = path.join(__dirname, '..', 'out')
const DIST_DIR = path.join(__dirname, '..', 'dist')

// Ensure assets directory exists
if (!fs.existsSync(ASSETS_DIR)) {
  fs.mkdirSync(ASSETS_DIR, { recursive: true })
}

// Read or initialize build version
let buildData = { buildNumber: 0 }
if (fs.existsSync(VERSION_FILE)) {
  try {
    buildData = JSON.parse(fs.readFileSync(VERSION_FILE, 'utf8'))
  } catch (error) {
    console.warn('Warning: Could not read build version file, starting from 0')
  }
}

// Increment build number
buildData.buildNumber += 1
buildData.lastBuild = new Date().toISOString()

console.log(`Building with version number: ${buildData.buildNumber}`)

// Save updated build data
fs.writeFileSync(VERSION_FILE, JSON.stringify(buildData, null, 2))

// Create version.txt in assets directory (for development)
const versionContent = buildData.buildNumber.toString()
fs.writeFileSync(path.join(ASSETS_DIR, 'version.txt'), versionContent)

// Run the actual build
console.log('Running electron-vite build...')
try {
  execSync('electron-vite build', { stdio: 'inherit' })
  console.log('Build completed successfully')
} catch (error) {
  console.error('Build failed:', error.message)
  process.exit(1)
}

// Copy version.txt to output directories after build
if (fs.existsSync(OUT_DIR)) {
  // Copy to out directory root
  const outVersionPath = path.join(OUT_DIR, 'version.txt')
  fs.writeFileSync(outVersionPath, versionContent)
  console.log(`Version file created: ${outVersionPath}`)

  // Copy to renderer output for web builds
  const rendererOutDir = path.join(OUT_DIR, 'renderer')
  if (fs.existsSync(rendererOutDir)) {
    fs.writeFileSync(path.join(rendererOutDir, 'version.txt'), versionContent)
    console.log(`Version file copied to renderer output`)
  }
} else {
  console.warn('Warning: out directory not found, version.txt not copied')
}

// Also copy to dist directory if it exists (for electron-builder)
if (fs.existsSync(DIST_DIR)) {
  const distVersionPath = path.join(DIST_DIR, 'version.txt')
  fs.writeFileSync(distVersionPath, versionContent)
  console.log(`Version file created: ${distVersionPath}`)
}

console.log(`Build completed with version: ${buildData.buildNumber}`)
