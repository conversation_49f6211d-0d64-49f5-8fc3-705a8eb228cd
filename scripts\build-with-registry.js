const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 项目根目录
const PROJECT_ROOT = path.resolve(__dirname, '..')
const BUILD_DIR = path.join(PROJECT_ROOT, 'build')
const INSTALLER_NSH = path.join(BUILD_DIR, 'installer.nsh')

console.log('🚀 开始构建蓝奥智饮应用...')

/**
 * 验证注册表配置文件
 */
function validateRegistryConfig() {
  console.log('📋 验证注册表配置文件...')

  if (!fs.existsSync(INSTALLER_NSH)) {
    console.error('❌ 错误: installer.nsh 文件不存在')
    process.exit(1)
  }

  const content = fs.readFileSync(INSTALLER_NSH, 'utf8')

  // 检查必要的宏和函数是否存在
  const requiredElements = [
    '!macro customInstall',
    '!macro customUnInstall',
    'Function DisableEdgeSwipeGesture',
    'Function un.RestoreEdgeSwipeGesture',
  ]

  const missingElements = requiredElements.filter((element) => !content.includes(element))

  if (missingElements.length > 0) {
    console.error('❌ 错误: installer.nsh 文件缺少必要的元素:')
    missingElements.forEach((element) => console.error(`   - ${element}`))
    process.exit(1)
  }

  console.log('✅ 注册表配置文件验证通过')
}

/**
 * 运行构建前的注册表检查
 */
function preBuildRegistryCheck() {
  console.log('🔍 执行构建前注册表检查...')

  try {
    // 检查当前系统是否已经设置了边缘滑动禁用
    const checkScript = `
      $regPath = "HKCU:\\SOFTWARE\\Policies\\Microsoft\\Windows\\EdgeUI"
      $valueName = "AllowEdgeSwipe"
      
      try {
        if (Test-Path $regPath) {
          $value = Get-ItemProperty -Path $regPath -Name $valueName -ErrorAction SilentlyContinue
          if ($value -and $value.$valueName -eq 0) {
            Write-Host "✅ 边缘滑动手势已被禁用"
            exit 0
          } else {
            Write-Host "⚠️  边缘滑动手势未被禁用，将在安装时配置"
            exit 0
          }
        } else {
          Write-Host "ℹ️  注册表路径不存在，将在安装时创建"
          exit 0
        }
      } catch {
        Write-Host "⚠️  无法检查注册表状态: $($_.Exception.Message)"
        exit 0
      }
    `

    execSync(`powershell -Command "${checkScript}"`, {
      stdio: 'inherit',
      encoding: 'utf8',
    })
  } catch (error) {
    console.warn('⚠️  注册表检查失败，但继续构建:', error.message)
  }
}

/**
 * 运行主构建过程
 */
function runMainBuild() {
  console.log('🔨 开始主构建过程...')

  try {
    // 运行原有的构建脚本
    execSync('node scripts/build-with-version.js', {
      stdio: 'inherit',
      cwd: PROJECT_ROOT,
    })

    console.log('✅ 主构建过程完成')
  } catch (error) {
    console.error('❌ 主构建过程失败:', error.message)
    process.exit(1)
  }
}

/**
 * 运行 electron-builder 打包
 */
function runElectronBuilder() {
  console.log('📦 开始 Electron 应用打包...')

  try {
    // 根据平台选择构建命令
    const platform = process.platform
    let buildCommand = 'electron-builder'

    if (platform === 'win32') {
      buildCommand += ' --win'
    } else if (platform === 'darwin') {
      buildCommand += ' --mac'
    } else {
      buildCommand += ' --linux'
    }

    console.log(`🎯 执行命令: ${buildCommand}`)

    execSync(buildCommand, {
      stdio: 'inherit',
      cwd: PROJECT_ROOT,
    })

    console.log('✅ Electron 应用打包完成')
  } catch (error) {
    console.error('❌ Electron 应用打包失败:', error.message)
    process.exit(1)
  }
}

/**
 * 构建后验证
 */
function postBuildValidation() {
  console.log('🔍 执行构建后验证...')

  const distDir = path.join(PROJECT_ROOT, 'dist')

  if (!fs.existsSync(distDir)) {
    console.error('❌ 错误: dist 目录不存在')
    process.exit(1)
  }

  // 查找生成的安装包
  const files = fs.readdirSync(distDir)
  const installerFiles = files.filter(
    (file) =>
      file.endsWith('.exe') ||
      file.endsWith('.dmg') ||
      file.endsWith('.AppImage') ||
      file.endsWith('.deb'),
  )

  if (installerFiles.length === 0) {
    console.error('❌ 错误: 未找到生成的安装包')
    process.exit(1)
  }

  console.log('✅ 构建验证通过，生成的安装包:')
  installerFiles.forEach((file) => {
    const filePath = path.join(distDir, file)
    const stats = fs.statSync(filePath)
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2)
    console.log(`   📄 ${file} (${sizeInMB} MB)`)
  })
}

/**
 * 显示构建摘要
 */
function showBuildSummary() {
  console.log('\n🎉 构建完成摘要:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('✅ 应用构建成功')
  console.log('✅ 注册表配置已集成到安装包')
  console.log('✅ 安装时将自动禁用Windows边缘滑动手势')
  console.log('✅ 卸载时将自动恢复Windows边缘滑动手势')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('\n📝 注意事项:')
  console.log('   • 安装包需要管理员权限才能修改系统注册表')
  console.log('   • 注册表修改在重启后生效')
  console.log('   • 卸载应用时会自动恢复原始设置')
  console.log('')
}

// 主执行流程
async function main() {
  try {
    console.log('🏗️  蓝奥智饮应用构建工具 v1.0')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')

    // 1. 验证注册表配置
    validateRegistryConfig()

    // 2. 构建前注册表检查
    preBuildRegistryCheck()

    // 3. 运行主构建
    runMainBuild()

    // 4. 运行 electron-builder
    runElectronBuilder()

    // 5. 构建后验证
    postBuildValidation()

    // 6. 显示构建摘要
    showBuildSummary()
  } catch (error) {
    console.error('\n❌ 构建过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason)
  process.exit(1)
})

// 启动主流程
main()
