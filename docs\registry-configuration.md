# 蓝奥智饮应用 - 注册表配置说明

## 概述

蓝奥智饮应用在安装时会自动配置Windows注册表，禁用边缘滑动手势功能，以防止用户在使用触摸屏时意外触发系统功能。

## 功能说明

### 禁用的功能
- **Windows边缘滑动手势**: 防止从屏幕边缘滑动时触发系统功能（如任务切换、通知中心等）

### 注册表修改位置
- `HKEY_CURRENT_USER\SOFTWARE\Policies\Microsoft\Windows\EdgeUI`
- `HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\EdgeUI`

### 修改的注册表值
- **键名**: `AllowEdgeSwipe`
- **类型**: `REG_DWORD`
- **值**: `0` (禁用) / `1` (启用)

## 构建和打包

### 使用增强的构建脚本

```bash
# 使用带注册表配置的构建脚本（推荐）
npm run build:registry

# 或者使用 Windows 专用构建命令
npm run build:win
```

### 传统构建方式

```bash
# 简单构建（不包含注册表配置验证）
npm run build:win-simple
```

## 构建脚本功能

### `scripts/build-with-registry.js`

这个增强的构建脚本提供以下功能：

1. **构建前验证**
   - 检查 `installer.nsh` 文件是否存在
   - 验证必要的NSIS宏和函数
   - 检查当前系统的注册表状态

2. **构建过程**
   - 运行标准的应用构建流程
   - 集成注册表配置到安装包
   - 生成带有注册表修改功能的安装程序

3. **构建后验证**
   - 验证安装包是否成功生成
   - 显示安装包信息和大小
   - 提供构建摘要和注意事项

## 安装包行为

### 安装时
1. 显示"正在配置系统注册表以禁用边缘滑动手势..."
2. 尝试在以下位置创建/修改注册表项：
   - HKCU (当前用户) - 64位和32位视图
   - HKLM (本地机器) - 64位和32位视图
3. 显示每个操作的成功/失败状态
4. 完成后显示"注册表配置完成"

### 卸载时
1. 显示"正在恢复系统注册表设置..."
2. 删除之前创建的注册表项
3. 恢复系统默认的边缘滑动手势设置
4. 完成后显示"注册表恢复完成"

## 权限要求

### 管理员权限
- 安装包需要管理员权限才能修改 `HKEY_LOCAL_MACHINE` 注册表
- 如果没有管理员权限，只会修改 `HKEY_CURRENT_USER` 注册表

### 配置生效
- 注册表修改在系统重启后完全生效
- 某些情况下可能需要重新登录用户账户

## 故障排除

### 常见问题

1. **构建失败 - installer.nsh 文件不存在**
   ```
   解决方案: 确保 build/installer.nsh 文件存在
   ```

2. **安装时注册表修改失败**
   ```
   原因: 缺少管理员权限
   解决方案: 右键点击安装包，选择"以管理员身份运行"
   ```

3. **边缘滑动手势仍然有效**
   ```
   原因: 注册表修改需要重启生效
   解决方案: 重启计算机或重新登录用户账户
   ```

### 手动验证注册表修改

可以使用以下PowerShell命令检查注册表状态：

```powershell
# 检查当前用户注册表
Get-ItemProperty -Path "HKCU:\SOFTWARE\Policies\Microsoft\Windows\EdgeUI" -Name "AllowEdgeSwipe" -ErrorAction SilentlyContinue

# 检查本地机器注册表
Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EdgeUI" -Name "AllowEdgeSwipe" -ErrorAction SilentlyContinue
```

### 手动恢复设置

如果需要手动恢复边缘滑动手势：

```powershell
# 删除当前用户设置
Remove-ItemProperty -Path "HKCU:\SOFTWARE\Policies\Microsoft\Windows\EdgeUI" -Name "AllowEdgeSwipe" -ErrorAction SilentlyContinue

# 删除本地机器设置（需要管理员权限）
Remove-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EdgeUI" -Name "AllowEdgeSwipe" -ErrorAction SilentlyContinue
```

## 开发说明

### 修改注册表配置

如果需要修改注册表配置，请编辑以下文件：
- `build/installer.nsh` - NSIS安装脚本
- `scripts/build-with-registry.js` - 构建验证脚本

### 添加新的注册表项

1. 在 `installer.nsh` 中的 `DisableEdgeSwipeGesture` 函数中添加新的注册表操作
2. 在 `un.RestoreEdgeSwipeGesture` 函数中添加对应的恢复操作
3. 更新 `build-with-registry.js` 中的验证逻辑

## 安全考虑

- 注册表修改仅限于边缘滑动手势相关设置
- 不会修改系统安全设置或其他敏感配置
- 卸载时会完全恢复原始设置
- 所有操作都有详细的日志记录
