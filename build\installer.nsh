; 蓝奥智饮应用安装器注册表配置
; 此文件包含安装和卸载时需要修改的注册表项

!macro customInstall
  ; 显示安装进度信息
  DetailPrint "正在配置系统注册表..."

  ; 禁用Windows边缘滑动手势 (EdgeUI)
  Call DisableEdgeSwipe

  ; 禁用Windows触摸键盘和手写面板服务
  Call DisableTouchKeyboard

  ; 配置应用自启动相关注册表
  Call ConfigureAutoStart

  ; 配置系统安全策略
  Call ConfigureSecurityPolicy

  ; 配置网络相关设置
  Call ConfigureNetworkSettings

  DetailPrint "注册表配置完成"
!macroend

!macro customUnInstall
  ; 显示卸载进度信息
  DetailPrint "正在清理系统注册表..."

  ; 恢复Windows边缘滑动手势
  Call un.RestoreEdgeSwipe

  ; 恢复触摸键盘和手写面板服务
  Call un.RestoreTouchKeyboard

  ; 清理自启动相关注册表
  Call un.CleanupAutoStart

  ; 恢复系统安全策略
  Call un.RestoreSecurityPolicy

  ; 恢复网络相关设置
  Call un.RestoreNetworkSettings

  DetailPrint "注册表清理完成"
!macroend

; ==================== 安装时的注册表配置函数 ====================

; 禁用Windows边缘滑动手势
Function DisableEdgeSwipe
  ; 64位注册表
  SetRegView 64
  WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0

  ; 32位注册表
  SetRegView 32
  WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0

  DetailPrint "已禁用Windows边缘滑动手势"
FunctionEnd

; 禁用触摸键盘和手写面板
Function DisableTouchKeyboard
  ; 禁用触摸键盘自动弹出
  SetRegView 64
  WriteRegDWORD HKCU "SOFTWARE\Microsoft\TabletTip\1.7" "EnableAutocorrection" 0
  WriteRegDWORD HKCU "SOFTWARE\Microsoft\TabletTip\1.7" "EnableSpellchecking" 0
  WriteRegDWORD HKLM "SOFTWARE\Microsoft\TabletTip\1.7" "EnableDesktopModeAutoInvoke" 0

  ; 禁用手写面板
  WriteRegDWORD HKCU "SOFTWARE\Microsoft\Windows\CurrentVersion\PenWorkspace" "PenWorkspaceAppSuggestionsEnabled" 0

  DetailPrint "已禁用触摸键盘和手写面板"
FunctionEnd

; 配置应用自启动
Function ConfigureAutoStart
  ; 添加应用到启动项
  WriteRegStr HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Run" "蓝奥智饮" "$INSTDIR\蓝奥智饮.exe"

  ; 设置应用优先级
  WriteRegDWORD HKLM "SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options\蓝奥智饮.exe\PerfOptions" "CpuPriorityClass" 3

  DetailPrint "已配置应用自启动"
FunctionEnd

; 配置系统安全策略
Function ConfigureSecurityPolicy
  ; 禁用Windows Defender实时保护对应用的扫描
  WriteRegDWORD HKLM "SOFTWARE\Microsoft\Windows Defender\Exclusions\Paths" "$INSTDIR" 0

  ; 禁用UAC对应用的提示
  WriteRegDWORD HKLM "SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" "ConsentPromptBehaviorUser" 0

  DetailPrint "已配置系统安全策略"
FunctionEnd

; 配置网络相关设置
Function ConfigureNetworkSettings
  ; 设置网络配置文件为专用网络
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\NetworkConnectivityStatusIndicator" "NoActiveProbe" 1

  ; 禁用网络位置向导
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\Network Connections" "NC_ShowSharedAccessUI" 0

  DetailPrint "已配置网络相关设置"
FunctionEnd
