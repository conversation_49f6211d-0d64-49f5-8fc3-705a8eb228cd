; 蓝奥智饮应用安装器 - 注册表配置脚本
; 功能：禁用Windows边缘滑动手势，防止意外触发系统功能

!macro customInstall
  DetailPrint "正在配置系统注册表以禁用边缘滑动手势..."

  ; 调用禁用边缘滑动的函数
  Call DisableEdgeSwipeGesture

  DetailPrint "注册表配置完成"
!macroend

!macro customUnInstall
  DetailPrint "正在恢复系统注册表设置..."

  ; 调用恢复边缘滑动的函数
  Call un.RestoreEdgeSwipeGesture

  DetailPrint "注册表恢复完成"
!macroend

; ==================== 安装时禁用边缘滑动手势 ====================
Function DisableEdgeSwipeGesture
  Push $0
  Push $1

  ; 创建注册表键路径（如果不存在）
  DetailPrint "创建注册表键路径..."

  ; 64位注册表视图
  SetRegView 64

  ; 为当前用户创建策略键
  WriteRegStr HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "" ""
  ClearErrors
  WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  IfErrors 0 +3
    DetailPrint "警告: 无法写入HKCU 64位注册表"
    Goto try_hklm_64
  DetailPrint "成功禁用HKCU 64位边缘滑动手势"

  try_hklm_64:
  ; 为本地机器创建策略键（需要管理员权限）
  WriteRegStr HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "" ""
  ClearErrors
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  IfErrors 0 +3
    DetailPrint "警告: 无法写入HKLM 64位注册表"
    Goto try_32bit
  DetailPrint "成功禁用HKLM 64位边缘滑动手势"

  try_32bit:
  ; 32位注册表视图
  SetRegView 32

  ; 为当前用户创建策略键
  WriteRegStr HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "" ""
  ClearErrors
  WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  IfErrors 0 +3
    DetailPrint "警告: 无法写入HKCU 32位注册表"
    Goto try_hklm_32
  DetailPrint "成功禁用HKCU 32位边缘滑动手势"

  try_hklm_32:
  ; 为本地机器创建策略键
  WriteRegStr HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "" ""
  ClearErrors
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  IfErrors 0 +3
    DetailPrint "警告: 无法写入HKLM 32位注册表"
    Goto done
  DetailPrint "成功禁用HKLM 32位边缘滑动手势"

  done:
  ; 恢复默认注册表视图
  SetRegView 64

  Pop $1
  Pop $0

  DetailPrint "边缘滑动手势禁用配置完成"
FunctionEnd

; ==================== 卸载时恢复边缘滑动手势 ====================
Function un.RestoreEdgeSwipeGesture
  Push $0
  Push $1

  DetailPrint "正在恢复边缘滑动手势设置..."

  ; 64位注册表视图
  SetRegView 64

  ; 删除当前用户的设置
  ClearErrors
  DeleteRegValue HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"
  IfErrors 0 +2
    DetailPrint "注意: HKCU 64位注册表项可能不存在"

  ; 删除本地机器的设置
  ClearErrors
  DeleteRegValue HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"
  IfErrors 0 +2
    DetailPrint "注意: HKLM 64位注册表项可能不存在"

  ; 32位注册表视图
  SetRegView 32

  ; 删除当前用户的设置
  ClearErrors
  DeleteRegValue HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"
  IfErrors 0 +2
    DetailPrint "注意: HKCU 32位注册表项可能不存在"

  ; 删除本地机器的设置
  ClearErrors
  DeleteRegValue HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"
  IfErrors 0 +2
    DetailPrint "注意: HKLM 32位注册表项可能不存在"

  ; 恢复默认注册表视图
  SetRegView 64

  Pop $1
  Pop $0

  DetailPrint "边缘滑动手势设置恢复完成"
FunctionEnd
