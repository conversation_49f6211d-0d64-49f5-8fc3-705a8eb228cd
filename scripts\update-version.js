#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Get version from command line argument or increment current version
const newVersion = process.argv[2]

// Paths to update
const paths = [
  path.join(__dirname, '..', 'public', 'version.txt'),
  path.join(__dirname, '..', 'out', 'version.txt'),
  path.join(__dirname, '..', 'out', 'renderer', 'version.txt'),
  path.join(__dirname, '..', 'dist', 'version.txt'),
]

if (newVersion) {
  // Set specific version
  console.log(`Setting version to: ${newVersion}`)
  paths.forEach((filePath) => {
    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    fs.writeFileSync(filePath, newVersion)
    console.log(`✓ Updated: ${filePath}`)
  })
} else {
  // Increment current version
  let currentVersion = 1
  const publicVersionPath = paths[0]

  if (fs.existsSync(publicVersionPath)) {
    try {
      currentVersion = parseInt(fs.readFileSync(publicVersionPath, 'utf8').trim(), 10) || 1
    } catch (error) {
      console.warn('Could not read current version, starting from 1')
    }
  }

  const nextVersion = currentVersion + 1
  console.log(`Incrementing version from ${currentVersion} to ${nextVersion}`)

  paths.forEach((filePath) => {
    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    fs.writeFileSync(filePath, nextVersion.toString())
    console.log(`✓ Updated: ${filePath}`)
  })
}

console.log(
  '\nVersion update complete! This should trigger auto-refresh if the app is running and user is idle.',
)
