<template>
  <MenuList v-if="online" />
  <Offline v-else />
</template>
<script lang="ts" setup>
import MenuList from './goods-menu/components/menu.vue'
import Offline from '../../components/offline.vue'
import { useRoute } from 'vue-router'
import { useOnline } from '@vueuse/core'
import { useAutoUpdate } from '../../composables/useAutoUpdate'
import { watch, computed } from 'vue'

const route = useRoute()
const online = useOnline()

// Auto-update system - only active when on exact /consumer/home route
const isHomeRoute = computed(() => route.path === '/consumer/home')
const autoUpdate = useAutoUpdate({
  idleThreshold: 5 * 60 * 1000, // 5 minutes
  pollingInterval: 60 * 1000, // 60 seconds
  versionUrl: '/version.txt',
  storageKey: 'coffee-app-version',
})

// Watch route changes to control auto-update
watch(
  isHomeRoute,
  (isHome) => {
    if (!isHome) {
      // Stop polling when leaving /home route
      autoUpdate.stopPolling()
    }
    // Polling will automatically start when user becomes idle on /consumer/home route
  },
  { immediate: true },
)

const carId = (route.query.carId as string) || import.meta.env.VITE_CAR_ID
if (carId && !localStorage.getItem('carId')) {
  localStorage.setItem('carId', carId)
}
</script>
