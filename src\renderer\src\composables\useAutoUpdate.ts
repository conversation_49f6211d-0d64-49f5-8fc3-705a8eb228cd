import { ref, onMounted, onUnmounted, watch, readonly } from 'vue'
import { useEventListener } from '@vueuse/core'

interface AutoUpdateOptions {
  idleThreshold?: number // milliseconds until considered idle (default: 5 minutes)
  pollingInterval?: number // milliseconds between version checks (default: 30 seconds)
  versionUrl?: string // URL to fetch version from (default: '/version.txt')
  storageKey?: string // localStorage key for current version (default: 'app-version')
}

export function useAutoUpdate(options: AutoUpdateOptions = {}) {
  const {
    idleThreshold = 5 * 60 * 1000, // 5 minutes
    pollingInterval = 30 * 1000, // 30 seconds
    versionUrl = '/version.txt',
    storageKey = 'app-version',
  } = options

  // State
  const isIdle = ref(false)
  const isPolling = ref(false)
  const currentVersion = ref<string | null>(null)
  const lastActivity = ref(Date.now())
  const error = ref<string | null>(null)

  // Timers
  let idleTimer: number | null = null
  let pollingTimer: number | null = null

  // Initialize current version from localStorage
  const initializeVersion = () => {
    const stored = localStorage.getItem(storageKey)
    if (stored) {
      currentVersion.value = stored
    }
  }

  // Save version to localStorage
  const saveVersion = (version: string) => {
    localStorage.setItem(storageKey, version)
    currentVersion.value = version
  }

  // Reset idle timer
  const resetIdleTimer = () => {
    lastActivity.value = Date.now()
    isIdle.value = false

    if (idleTimer) {
      clearTimeout(idleTimer)
    }

    idleTimer = window.setTimeout(() => {
      isIdle.value = true
    }, idleThreshold)
  }

  // Fetch version from server
  const fetchVersion = async (): Promise<string | null> => {
    try {
      error.value = null
      const response = await fetch(versionUrl, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const version = (await response.text()).trim()
      return version
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch version'
      console.warn('Version fetch failed:', error.value)
      return null
    }
  }

  // Check for version updates
  const checkForUpdates = async () => {
    const latestVersion = await fetchVersion()

    if (latestVersion && currentVersion.value) {
      const current = parseInt(currentVersion.value, 10)
      const latest = parseInt(latestVersion, 10)

      if (!isNaN(current) && !isNaN(latest) && latest > current) {
        console.log(`New version detected: ${latest} (current: ${current})`)
        // Force page refresh
        window.location.reload()
        return true
      }
    } else if (latestVersion && !currentVersion.value) {
      // First time - just save the version
      saveVersion(latestVersion)
    }

    return false
  }

  // Start version polling
  const startPolling = () => {
    if (isPolling.value) return

    isPolling.value = true
    console.log('Starting version polling...')

    // Check immediately
    checkForUpdates()

    // Then check at intervals
    pollingTimer = window.setInterval(() => {
      checkForUpdates()
    }, pollingInterval)
  }

  // Stop version polling
  const stopPolling = () => {
    if (!isPolling.value) return

    isPolling.value = false
    console.log('Stopping version polling...')

    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
  }

  // Watch idle state to control polling
  watch(isIdle, (idle) => {
    if (idle) {
      startPolling()
    } else {
      stopPolling()
    }
  })

  // Activity events to track
  const activityEvents = [
    'mousedown',
    'mousemove',
    'keydown',
    'touchstart',
    'click',
    'scroll',
    'wheel',
  ]

  // Setup activity listeners
  onMounted(() => {
    initializeVersion()

    // Setup activity listeners
    activityEvents.forEach((event) => {
      useEventListener(window, event, resetIdleTimer, { passive: true })
    })

    // Initialize idle timer
    resetIdleTimer()
  })

  // Cleanup
  onUnmounted(() => {
    if (idleTimer) {
      clearTimeout(idleTimer)
    }
    stopPolling()
  })

  // Manual methods
  const forceCheck = async () => {
    return await checkForUpdates()
  }

  const resetVersion = () => {
    localStorage.removeItem(storageKey)
    currentVersion.value = null
  }

  return {
    // State
    isIdle: readonly(isIdle),
    isPolling: readonly(isPolling),
    currentVersion: readonly(currentVersion),
    error: readonly(error),

    // Methods
    forceCheck,
    resetVersion,
    startPolling,
    stopPolling,

    // Internal methods (for testing)
    _fetchVersion: fetchVersion,
    _resetIdleTimer: resetIdleTimer,
  }
}
